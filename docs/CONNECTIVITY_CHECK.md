# 连通性检查功能

## 概述

在暴力破解攻击开始之前，系统现在会自动检查目标的连通性。如果目标不可访问，程序将退出，避免浪费时间进行无效的攻击尝试。

## 功能特性

### 自动连通性检查
- 在开始暴力破解之前自动检查目标连通性
- 从YAML配置文件中自动提取目标URL
- 支持HTTP和HTTPS协议
- 设置10秒连接超时

### 检查逻辑
1. 从配置文件的`steps`中找到`navigate`动作
2. 提取目标URL
3. 解析URL获取基础地址（协议+主机+端口）
4. 发送HTTP GET请求测试连通性
5. 检查响应状态码（< 400为成功）

### 错误处理
- **连接超时**: 目标服务器无响应
- **连接错误**: 无法到达目标服务器
- **HTTP错误**: 服务器返回错误状态码（>= 400）
- **其他异常**: 网络或解析错误

## 使用方法

### 自动检查（推荐）
运行主程序时会自动进行连通性检查：

```bash
uv run python grafana_executor.py
```

输出示例：
```
--- Checking target connectivity ---
Checking connectivity to target: http://112.74.93.147:8081/login.php
❌ Target returned error status: 502
❌ Target is not accessible. Exiting brute-force attack.
```

### 手动测试连通性
可以单独测试连通性检查功能：

```bash
# 测试不可访问目标
uv run python test/test_connectivity.py

# 测试可访问目标
uv run python test/test_connectivity_accessible.py
```

## 配置要求

确保 `templates/` 目录下的YAML配置文件中包含`navigate`步骤，例如：

```yaml
steps:
  - name: "Navigate to Login Page"
    action: "navigate"
    parameters:
      url: "http://example.com/login.php"
      waitUntil: "networkidle"
    description: "Opens the browser and navigates to the login page."
```

字典文件应放在 `dicts/` 目录下：
- `dicts/username.txt` - 用户名字典
- `dicts/password.txt` - 密码字典

## 依赖项

新增了以下依赖项：
- `requests>=2.31.0` - 用于HTTP连通性检查
- `urllib.parse` - 用于URL解析（Python标准库）

## 状态指示

- ✅ **绿色**: 目标可访问，继续执行暴力破解
- ❌ **红色**: 目标不可访问，程序退出
- ⚠️ **黄色**: 警告信息（如配置文件中未找到URL）

## 项目结构

```
py_brute_mcp/
├── templates/              # YAML配置文件模板
│   ├── test-dvwa.yaml
│   ├── test-grafana.yaml
│   ├── test-minio.yaml
│   └── test-connectivity-demo.yaml
├── dicts/                  # 字典文件
│   ├── username.txt
│   └── password.txt
├── test/                   # 测试文件
│   ├── test_connectivity.py
│   ├── test_connectivity_accessible.py
│   └── test_fastmcp_client.py
├── grafana_executor.py     # 主执行文件
└── ...
```

## 测试文件

- `test/test_connectivity.py` - 测试不可访问目标
- `test/test_connectivity_accessible.py` - 测试可访问目标
- `templates/test-connectivity-demo.yaml` - 用于测试的配置文件（使用httpbin.org）

## 优势

1. **节省时间**: 避免对不可访问目标进行无效攻击
2. **早期发现问题**: 在开始攻击前识别网络或配置问题
3. **用户友好**: 清晰的状态指示和错误信息
4. **自动化**: 无需手动检查，完全自动化流程
