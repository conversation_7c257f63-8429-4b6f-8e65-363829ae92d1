#!/usr/bin/env python3
"""
测试连通性检查功能的简单脚本 - 使用可访问的目标
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from executor import check_target_connectivity

def test_connectivity_accessible():
    """测试连通性检查功能 - 可访问目标"""
    config_file = "templates/test-connectivity-demo.yaml"
    
    print("Testing connectivity check function with accessible target...")
    result = check_target_connectivity(config_file)
    
    if result:
        print("✅ Connectivity check passed - target is accessible")
    else:
        print("❌ Connectivity check failed - target is not accessible")
    
    return result

if __name__ == "__main__":
    test_connectivity_accessible()
